import { useState } from 'react';
import { useAuth } from '@/auth/context/auth-context';
import { useNavigate, Link } from 'react-router-dom';
import { Alert, AlertIcon, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinners';
import { AlertCircle, ArrowLeft, Shield } from 'lucide-react';
import { AuthBrandedLayout } from '@/auth/layouts/branded';

export function TwoFactorAuthPage() {
  const [code, setCode] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setIsProcessing(true);
      setError(null);

      if (!code.trim()) {
        setError('Verification code is required');
        return;
      }

      // In demo mode, accept any 6-digit code
      if (code.length === 6) {
        // Simulate successful 2FA verification
        await login('<EMAIL>', 'demo123');
        navigate('/');
      } else {
        setError('Please enter a valid 6-digit code');
      }
    } catch (err) {
      console.error('2FA verification error:', err);
      setError(err.message || 'Invalid verification code. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <AuthBrandedLayout 
      title="Two-Factor Authentication" 
      subtitle="Enter the 6-digit code from your authenticator app."
    >
      <div className="space-y-6">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 dark:bg-green-900 mb-4">
            <Shield className="h-8 w-8 text-green-600 dark:text-green-400" />
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            For demo purposes, enter any 6-digit code (e.g., 123456)
          </p>
        </div>

        <form className="space-y-6" onSubmit={handleSubmit}>
          {error && (
            <Alert variant="destructive">
              <AlertIcon>
                <AlertCircle className="h-4 w-4" />
              </AlertIcon>
              <AlertTitle>{error}</AlertTitle>
            </Alert>
          )}
          
          <div>
            <Label htmlFor="code">Verification Code</Label>
            <Input
              id="code"
              name="code"
              type="text"
              maxLength="6"
              required
              value={code}
              onChange={(e) => setCode(e.target.value.replace(/\D/g, ''))}
              placeholder="Enter 6-digit code"
              className="text-center text-lg tracking-widest"
            />
          </div>

          <div>
            <Button
              type="submit"
              className="w-full"
              disabled={isProcessing || code.length !== 6}
            >
              {isProcessing ? (
                <>
                  <Spinner className="mr-2 h-4 w-4" />
                  Verifying...
                </>
              ) : (
                'Verify Code'
              )}
            </Button>
          </div>

          <div className="text-center space-y-2">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Didn't receive a code?{' '}
              <button type="button" className="text-blue-600 hover:text-blue-500">
                Resend
              </button>
            </p>
            
            <Link to="/auth/signin">
              <Button variant="ghost" className="w-full">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Sign In
              </Button>
            </Link>
          </div>
        </form>
      </div>
    </AuthBrandedLayout>
  );
}
