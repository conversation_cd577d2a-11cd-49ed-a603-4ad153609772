import { useState, useEffect } from 'react';
import { AuthContext } from '../context/auth-context';

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [auth, setAuth] = useState(null);
  const [loading, setLoading] = useState(true);

  // Demo credentials
  const DEMO_USER = {
    id: 'demo-user-id',
    email: '<EMAIL>',
    fullname: 'Demo User',
    first_name: 'Demo',
    last_name: 'User',
    username: 'demo',
  };

  const DEMO_AUTH = {
    access_token: 'demo-access-token',
    refresh_token: 'demo-refresh-token',
    user: DEMO_USER,
  };

  useEffect(() => {
    // Check if user is already logged in
    const savedAuth = localStorage.getItem('auth');
    if (savedAuth) {
      try {
        const parsedAuth = JSON.parse(savedAuth);
        setAuth(parsedAuth);
        setUser(parsedAuth.user);
      } catch (error) {
        console.error('Error parsing saved auth:', error);
        localStorage.removeItem('auth');
      }
    }
    setLoading(false);
  }, []);

  const login = async (email, password) => {
    setLoading(true);
    try {
      // Simple demo login check - you can customize these credentials
      if (email === '<EMAIL>' && password === 'demo123') {
        setAuth(DEMO_AUTH);
        setUser(DEMO_USER);
        localStorage.setItem('auth', JSON.stringify(DEMO_AUTH));
        return { success: true };
      } else {
        throw new Error('Invalid credentials. Use <EMAIL> / demo123');
      }
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setAuth(null);
    setUser(null);
    localStorage.removeItem('auth');
  };

  const verify = async () => {
    // Simple verification - just check if auth exists
    return auth ? { success: true } : { success: false };
  };

  const saveAuth = (authData) => {
    setAuth(authData);
    setUser(authData.user);
    localStorage.setItem('auth', JSON.stringify(authData));
  };

  const register = async (userData) => {
    setLoading(true);
    try {
      // In demo mode, we'll just simulate registration
      console.log('Demo registration for:', userData.email);
      // You could store user data in localStorage here if needed
      return { success: true, message: 'Account created successfully! Please sign in.' };
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const requestPasswordReset = async (email) => {
    setLoading(true);
    try {
      // In demo mode, we'll just simulate password reset
      console.log('Demo password reset requested for:', email);
      return { success: true, message: 'Password reset link sent!' };
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async () => {
    throw new Error('Password reset not implemented in demo mode');
  };

  const resendVerificationEmail = async () => {
    throw new Error('Email verification not implemented in demo mode');
  };

  const getUser = async () => {
    return user;
  };

  const updateProfile = async () => {
    return {};
  };

  const value = {
    user,
    auth,
    loading,
    setLoading,
    saveAuth,
    setUser,
    login,
    register,
    requestPasswordReset,
    resetPassword,
    resendVerificationEmail,
    getUser,
    updateProfile,
    logout,
    verify,
    isAdmin: false,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
