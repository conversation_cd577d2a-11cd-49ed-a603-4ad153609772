import { useEffect, useState } from 'react';
import { LocalAuthAdapter } from '@/auth/simple_auth';
import { AuthContext } from '@/auth/context/auth-context';
import * as authHelper from '@/auth/lib/helpers';

// Define the Supabase Auth Provider
export function SupabaseAuthProvider({ children }) {
  const [auth, setAuth] = useState(authHelper.getAuth());
  const [currentUser, setCurrentUser] = useState(undefined);
  const [loading, setLoading] = useState(true);

  // Save auth to localStorage
  const saveAuth = (authModel) => {
    setAuth(authModel);
    authHelper.saveAuth(authModel);
  };

  // Check if user is admin
  const isAdmin = () => {
    return currentUser?.is_admin || false;
  };

  // Verify authentication on mount
  const verify = async () => {
    try {
      setLoading(true);
      const user = await getUser();
      if (user) {
        setCurrentUser(user);
      } else {
        saveAuth(undefined);
        setCurrentUser(undefined);
      }
    } catch (error) {
      console.error('Auth verification error:', error);
      saveAuth(undefined);
      setCurrentUser(undefined);
    } finally {
      setLoading(false);
    }
  };

  // Initialize auth on mount
  useEffect(() => {
    verify();
  }, []);

  // Clear auth if no tokens
  useEffect(() => {
    if (!auth?.access_token) {
      authHelper.removeAuth();
    }
  }, [auth]);

  const login = async (email, password) => {
    try {
      const auth = await LocalAuthAdapter.login(email, password);
      saveAuth(auth);
      const user = await getUser();
      setCurrentUser(user || undefined);
    } catch (error) {
      saveAuth(undefined);
      throw error;
    }
  };

  const register = async (
    email,
    password,
    password_confirmation,
    firstName,
    lastName,
  ) => {
    try {
      const auth = await LocalAuthAdapter.register(
        email,
        password,
        password_confirmation,
        firstName,
        lastName,
      );
      saveAuth(auth);
      const user = await getUser();
      setCurrentUser(user || undefined);
    } catch (error) {
      saveAuth(undefined);
      throw error;
    }
  };

  const requestPasswordReset = async (email) => {
    await LocalAuthAdapter.requestPasswordReset(email);
  };

  const resetPassword = async (password, password_confirmation) => {
    await LocalAuthAdapter.resetPasswordWithToken(password, password_confirmation);
  };

  const resendVerificationEmail = async (email) => {
    await LocalAuthAdapter.resendVerificationEmail(email);
  };

  const getUser = async () => {
    return await LocalAuthAdapter.getCurrentUser();
  };

  const updateProfile = async (userData) => {
    return await LocalAuthAdapter.updateUserProfile(userData);
  };

  const logout = () => {
    LocalAuthAdapter.logout();
    saveAuth(undefined);
    setCurrentUser(undefined);
  };

  return (
    <AuthContext.Provider
      value={{
        loading,
        setLoading,
        auth,
        saveAuth,
        user: currentUser,
        setUser: setCurrentUser,
        login,
        register,
        requestPasswordReset,
        resetPassword,
        resendVerificationEmail,
        getUser,
        updateProfile,
        logout,
        verify,
        isAdmin,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}
