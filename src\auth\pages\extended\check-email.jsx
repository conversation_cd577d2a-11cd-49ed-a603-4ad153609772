import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, Mail } from 'lucide-react';
import { AuthBrandedLayout } from '@/auth/layouts/branded';

export function CheckEmailPage() {
  return (
    <AuthBrandedLayout 
      title="Check Your Email" 
      subtitle="We've sent a verification link to your email address."
    >
      <div className="text-center space-y-6">
        <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 dark:bg-blue-900">
          <Mail className="h-8 w-8 text-blue-600 dark:text-blue-400" />
        </div>
        
        <div>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Please check your email and click the verification link to activate your account.
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Didn't receive the email? Check your spam folder or try again.
          </p>
        </div>

        <div className="space-y-3">
          <Button className="w-full">
            Resend Verification Email
          </Button>
          
          <Link to="/auth/signin">
            <Button variant="ghost" className="w-full">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Sign In
            </Button>
          </Link>
        </div>
      </div>
    </AuthBrandedLayout>
  );
}
