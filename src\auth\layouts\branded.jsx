import { toAbsoluteUrl } from '@/lib/helpers';
import { Link } from 'react-router-dom';

export function AuthBrandedLayout({ children, title, subtitle }) {
  return (
    <div className="min-h-screen flex">
      {/* Left side - Branding */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 to-purple-700 relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="relative z-10 flex flex-col justify-center items-center text-white p-12">
          <div className="text-center">
            <img
              src={toAbsoluteUrl('/media/app/logo-light.svg')}
              alt="Logo"
              className="h-12 w-auto mx-auto mb-8"
            />
            <h2 className="text-3xl font-bold mb-4">
              Welcome to Swaras Academy
            </h2>
            <p className="text-lg opacity-90 max-w-md">
              Discover amazing courses and enhance your skills with our comprehensive learning platform.
            </p>
          </div>
        </div>

        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-white opacity-5 rounded-full -translate-y-32 translate-x-32"></div>
        <div className="absolute bottom-0 left-0 w-48 h-48 bg-white opacity-5 rounded-full translate-y-24 -translate-x-24"></div>
      </div>

      {/* Right side - Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          {/* Mobile logo */}
          <div className="lg:hidden text-center mb-8">
            <Link to="/" className="inline-block">
              <img
                src={toAbsoluteUrl('/media/app/mini-logo.svg')}
                alt="Logo"
                className="h-8 w-auto mx-auto"
              />
            </Link>
          </div>

          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              {title}
            </h1>
            {subtitle && (
              <p className="text-gray-600 dark:text-gray-400">
                {subtitle}
              </p>
            )}
          </div>

          {/* Content */}
          <div className="space-y-6">
            {children}
          </div>

          {/* Footer */}
          <div className="mt-8 text-center">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              © 2024 Swaras Academy. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
