import { Navigate, Route, Routes } from 'react-router-dom';
import { SignInPage } from './pages/signin-page';
import { SignUpPage } from './pages/signup-page';
import { ResetPasswordPage } from './pages/reset-password-page';
import { ClassicSignInPage } from './pages/classic/signin-page';
import { CheckEmailPage } from './pages/extended/check-email';
import { TwoFactorAuthPage } from './pages/extended/tfa';

/**
 * Handles all authentication related routes.
 * This component is mounted at /auth/* in the main application router.
 */
export function AuthRouting() {
  return (
    <Routes>
      {/* Index route to redirect to sign-in */}
      <Route index element={<Navigate to="signin" replace />} />

      {/* Branded Layout Routes */}
      <Route path="signin" element={<SignInPage />} />
      <Route path="signup" element={<SignUpPage />} />
      <Route path="reset-password" element={<ResetPasswordPage />} />
      <Route path="2fa" element={<TwoFactorAuthPage />} />
      <Route path="check-email" element={<CheckEmailPage />} />

      {/* Classic Layout Routes */}
      <Route path="classic/signin" element={<ClassicSignInPage />} />
      <Route path="classic/signup" element={<SignUpPage />} />
      <Route path="classic/reset-password" element={<ResetPasswordPage />} />
      <Route path="classic/2fa" element={<TwoFactorAuthPage />} />
      <Route path="classic/check-email" element={<CheckEmailPage />} />

      {/* Fallback */}
      <Route path="*" element={<Navigate to="signin" replace />} />
    </Routes>
  );
}
