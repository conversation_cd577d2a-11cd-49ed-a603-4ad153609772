import { Navigate, Route, Routes } from 'react-router-dom';
import { SignInPage } from './pages/signin-page';

/**
 * Handles all authentication related routes.
 * This component is mounted at /auth/* in the main application router.
 */
export function AuthRouting() {
  return (
    <Routes>
      {/* Index route to redirect to sign-in */}
      <Route index element={<Navigate to="signin" replace />} />
      <Route path="signin" element={<SignInPage />} />
      <Route path="*" element={<Navigate to="signin" replace />} />
    </Routes>
  );
}
