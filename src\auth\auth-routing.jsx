import { Navigate, Route, Routes } from 'react-router-dom';
import { SignInPage } from './pages/signin-page';
import { SignUpPage } from './pages/signup-page';

/**
 * Handles all authentication related routes.
 * This component is mounted at /auth/* in the main application router.
 */
export function AuthRouting() {
  return (
    <Routes>
      {/* Index route to redirect to sign-in */}
      <Route index element={<Navigate to="signin" replace />} />

      {/* Authentication Routes */}
      <Route path="signin" element={<SignInPage />} />
      <Route path="signup" element={<SignUpPage />} />

      {/* Fallback */}
      <Route path="*" element={<Navigate to="signin" replace />} />
    </Routes>
  );
}
