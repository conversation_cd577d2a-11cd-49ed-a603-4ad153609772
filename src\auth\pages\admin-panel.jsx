import { useState, useEffect } from 'react';
import { localAuth } from '@/auth/local_auth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export function AdminPanel() {
  const [users, setUsers] = useState([]);
  const [sessions, setSessions] = useState([]);
  const [currentUser, setCurrentUser] = useState(null);
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    loadData();
  }, [refreshKey]);

  const loadData = async () => {
    try {
      const allUsers = localAuth.getAllUsers();
      const allSessions = localAuth.getAllSessions();
      const user = await localAuth.getCurrentUser();
      
      setUsers(allUsers);
      setSessions(allSessions);
      setCurrentUser(user);
    } catch (error) {
      console.error('Error loading admin data:', error);
    }
  };

  const clearAllData = () => {
    if (confirm('Are you sure you want to clear all authentication data? This will reset everything to default.')) {
      localAuth.clearAllData();
      setRefreshKey(prev => prev + 1);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Local Authentication Admin Panel</h1>
        <div className="space-x-2">
          <Button onClick={() => setRefreshKey(prev => prev + 1)} variant="outline">
            Refresh Data
          </Button>
          <Button onClick={clearAllData} variant="destructive">
            Clear All Data
          </Button>
        </div>
      </div>

      {/* Current User */}
      <Card>
        <CardHeader>
          <CardTitle>Current Authenticated User</CardTitle>
        </CardHeader>
        <CardContent>
          {currentUser ? (
            <div className="space-y-2">
              <p><strong>ID:</strong> {currentUser.id}</p>
              <p><strong>Email:</strong> {currentUser.email}</p>
              <p><strong>Name:</strong> {currentUser.fullname}</p>
              <p><strong>Admin:</strong> {currentUser.is_admin ? 'Yes' : 'No'}</p>
              <p><strong>Verified:</strong> {currentUser.email_verified ? 'Yes' : 'No'}</p>
            </div>
          ) : (
            <p className="text-muted-foreground">No user currently authenticated</p>
          )}
        </CardContent>
      </Card>

      {/* All Users */}
      <Card>
        <CardHeader>
          <CardTitle>All Users ({users.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {users.map((user) => (
              <div key={user.id} className="border rounded-lg p-4 space-y-2">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold">{user.fullname || user.username}</h3>
                    <p className="text-sm text-muted-foreground">{user.email}</p>
                  </div>
                  <div className="flex gap-2">
                    {user.is_admin && <Badge variant="destructive">Admin</Badge>}
                    {user.email_verified && <Badge variant="secondary">Verified</Badge>}
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>ID:</strong> {user.id}
                  </div>
                  <div>
                    <strong>Username:</strong> {user.username}
                  </div>
                  <div>
                    <strong>Company:</strong> {user.company_name || 'N/A'}
                  </div>
                  <div>
                    <strong>Phone:</strong> {user.phone || 'N/A'}
                  </div>
                  <div>
                    <strong>Created:</strong> {formatDate(user.created_at)}
                  </div>
                  <div>
                    <strong>Updated:</strong> {formatDate(user.updated_at)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Active Sessions */}
      <Card>
        <CardHeader>
          <CardTitle>Active Sessions ({sessions.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {sessions.length > 0 ? (
              sessions.map((session) => (
                <div key={session.id} className="border rounded-lg p-4 space-y-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold">Session {session.id}</h3>
                      <p className="text-sm text-muted-foreground">User ID: {session.userId}</p>
                    </div>
                    <Badge variant={new Date(session.expires_at) > new Date() ? 'default' : 'destructive'}>
                      {new Date(session.expires_at) > new Date() ? 'Active' : 'Expired'}
                    </Badge>
                  </div>
                  <div className="grid grid-cols-1 gap-2 text-sm">
                    <div>
                      <strong>Created:</strong> {formatDate(session.created_at)}
                    </div>
                    <div>
                      <strong>Expires:</strong> {formatDate(session.expires_at)}
                    </div>
                    <div>
                      <strong>Last Accessed:</strong> {formatDate(session.last_accessed)}
                    </div>
                    <div className="break-all">
                      <strong>Access Token:</strong> 
                      <code className="text-xs bg-muted p-1 rounded ml-2">
                        {session.accessToken.substring(0, 50)}...
                      </code>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-muted-foreground">No active sessions</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* LocalStorage Data */}
      <Card>
        <CardHeader>
          <CardTitle>LocalStorage Keys</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {Object.keys(localStorage)
              .filter(key => key.startsWith('local_auth_'))
              .map(key => (
                <div key={key} className="flex justify-between items-center p-2 bg-muted rounded">
                  <code className="text-sm">{key}</code>
                  <Badge variant="outline">
                    {localStorage.getItem(key)?.length || 0} chars
                  </Badge>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>

      {/* Demo Credentials */}
      <Card>
        <CardHeader>
          <CardTitle>Demo Credentials</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="p-3 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-900">Regular User</h4>
              <p className="text-blue-700">Email: <EMAIL></p>
              <p className="text-blue-700">Password: demo123</p>
            </div>
            <div className="p-3 bg-red-50 rounded-lg">
              <h4 className="font-semibold text-red-900">Admin User</h4>
              <p className="text-red-700">Email: <EMAIL></p>
              <p className="text-red-700">Password: admin123</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
