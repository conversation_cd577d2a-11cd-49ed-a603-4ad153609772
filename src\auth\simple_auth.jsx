// Ultra Simple Demo Authentication
// Just checks demo credentials - no storage, no registration, no complex features

/**
 * Ultra Simple Demo Authentication
 * Only validates demo credentials
 */
class SimpleAuth {
  constructor() {
    // Demo credentials - only these work
    this.DEMO_CREDENTIALS = {
      '<EMAIL>': 'demo123',
      '<EMAIL>': 'admin123'
    };

    // Demo user data
    this.DEMO_USERS = {
      '<EMAIL>': {
        id: 'demo-user-123',
        email: '<EMAIL>',
        username: 'demo',
        first_name: 'Demo',
        last_name: 'User',
        fullname: 'Demo User',
        is_admin: false,
      },
      '<EMAIL>': {
        id: 'admin-user-456',
        email: '<EMAIL>',
        username: 'admin',
        first_name: 'Admin',
        last_name: 'User',
        fullname: 'Admin User',
        is_admin: true,
      }
    };
  }

  // Simple login - just check demo credentials
  async login(email, password) {
    console.log('SimpleAuth: Checking demo credentials for:', email);

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 300));

    // Check if credentials match
    const expectedPassword = this.DEMO_CREDENTIALS[email.toLowerCase()];
    if (!expectedPassword || expectedPassword !== password) {
      throw new Error('Invalid email or password. Use <EMAIL>/demo123 or <EMAIL>/admin123');
    }

    console.log('SimpleAuth: Login successful');

    // Get user data
    const user = this.DEMO_USERS[email.toLowerCase()];

    // Store current user in localStorage (minimal storage)
    localStorage.setItem('current_user', JSON.stringify(user));

    // Return simple tokens
    return {
      access_token: btoa(JSON.stringify({ userId: user.id, exp: Date.now() + 24 * 60 * 60 * 1000 })),
      refresh_token: btoa(JSON.stringify({ userId: user.id, type: 'refresh' })),
    };
  }

  // Get current user
  async getCurrentUser() {
    try {
      const userStr = localStorage.getItem('current_user');
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  // Simple logout
  logout() {
    console.log('SimpleAuth: Logging out user');
    localStorage.removeItem('current_user');
  }

  // Check if user is authenticated
  isAuthenticated() {
    return !!localStorage.getItem('current_user');
  }


}

// Create singleton instance
export const localAuth = new SimpleAuth();

// Export for backward compatibility - only login works, others show error messages
export const LocalAuthAdapter = {
  login: (email, password) => localAuth.login(email, password),
  getCurrentUser: () => localAuth.getCurrentUser(),
  logout: () => localAuth.logout(),

  // Registration page will show but throw error when submitted
  register: async () => {
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate loading
    throw new Error('Registration is not available. Please use demo credentials: <EMAIL> / demo123');
  },

  // Password reset page will show but throw error when submitted
  resetPassword: async () => {
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate loading
    throw new Error('Password reset is not available. Please use demo credentials: <EMAIL> / demo123');
  },

  // Other features disabled
  updatePassword: async () => {
    throw new Error('Password update not available in demo mode');
  },
  updateUserProfile: async () => {
    throw new Error('Profile update not available in demo mode');
  },
  requestPasswordReset: async () => {
    await new Promise(resolve => setTimeout(resolve, 500));
    throw new Error('Password reset not available. Use demo credentials: <EMAIL> / demo123');
  },
  resetPasswordWithToken: async () => {
    throw new Error('Password reset not available in demo mode');
  },
  resendVerificationEmail: async () => {
    throw new Error('Email verification not available in demo mode');
  },
  signInWithOAuth: async () => {
    throw new Error('OAuth authentication not available in demo mode');
  }
};

export default localAuth;
