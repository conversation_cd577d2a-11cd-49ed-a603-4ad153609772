
 */
class SimpleAuth {
  constructor() {
    // Demo credentials - only these work
    this.DEMO_CREDENTIALS = {
      '<EMAIL>': 'demo123',
      '<EMAIL>': 'admin123'
    };

    // Demo user data
    this.DEMO_USERS = {
      '<EMAIL>': {
        id: 'demo-user-123',
        email: '<EMAIL>',
        username: 'demo',
        first_name: 'Demo',
        last_name: 'User',
        fullname: 'Demo User',
        is_admin: false,
      },
      '<EMAIL>': {
        id: 'admin-user-456',
        email: '<EMAIL>',
        username: 'admin',
        first_name: 'Admin',
        last_name: 'User',
        fullname: 'Admin User',
        is_admin: true,
      }
    };
  }

  // Simple login - just check demo credentials
  async login(email, password) {
    console.log('SimpleAuth: Checking demo credentials for:', email);

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 300));

    // Check if credentials match
    const expectedPassword = this.DEMO_CREDENTIALS[email.toLowerCase()];
    if (!expectedPassword || expectedPassword !== password) {
      throw new Error('Invalid email or password. Use <EMAIL>/demo123 or <EMAIL>/admin123');
    }

    console.log('SimpleAuth: Login successful');

    // Get user data
    const user = this.DEMO_USERS[email.toLowerCase()];

    // Store current user in localStorage (minimal storage)
    localStorage.setItem('current_user', JSON.stringify(user));

    // Return simple tokens
    return {
      access_token: btoa(JSON.stringify({ userId: user.id, exp: Date.now() + 24 * 60 * 60 * 1000 })),
      refresh_token: btoa(JSON.stringify({ userId: user.id, type: 'refresh' })),
    };
  }

  // Get current user
  async getCurrentUser() {
    try {
      const userStr = localStorage.getItem('current_user');
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  // Simple logout
  logout() {
    console.log('SimpleAuth: Logging out user');
    localStorage.removeItem('current_user');
  }

  // Check if user is authenticated
  isAuthenticated() {
    return !!localStorage.getItem('current_user');
  }


}

const localAuth = new SimpleAuth();


export default localAuth;
