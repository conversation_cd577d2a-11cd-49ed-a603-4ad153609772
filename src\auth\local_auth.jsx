// Local Authentication System
// This file handles all authentication operations and data storage locally
// No external dependencies - everything is stored in localStorage

/**
 * Local Authentication Database
 * Stores users, sessions, and authentication data in localStorage
 */
class LocalAuthDB {
  constructor() {
    this.USERS_KEY = 'local_auth_users';
    this.SESSIONS_KEY = 'local_auth_sessions';
    this.CURRENT_SESSION_KEY = 'local_auth_current_session';
    this.RESET_TOKENS_KEY = 'local_auth_reset_tokens';
    
    // Initialize with demo users if not exists
    this.initializeDefaultUsers();
  }

  // Initialize default demo users
  initializeDefaultUsers() {
    const users = this.getUsers();
    if (users.length === 0) {
      const defaultUsers = [
        {
          id: 'demo-user-123',
          email: '<EMAIL>',
          password: 'demo123', // In real app, this would be hashed
          username: 'demo',
          first_name: 'Demo',
          last_name: 'User',
          fullname: 'Demo User',
          occupation: 'Developer',
          company_name: 'KT Company',
          companyName: 'KT Company',
          phone: '+1234567890',
          roles: ['user'],
          pic: '',
          language: 'en',
          is_admin: false,
          email_verified: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: 'admin-user-456',
          email: '<EMAIL>',
          password: 'admin123',
          username: 'admin',
          first_name: 'Admin',
          last_name: 'User',
          fullname: 'Admin User',
          occupation: 'Administrator',
          company_name: 'KT Company',
          companyName: 'KT Company',
          phone: '+1234567891',
          roles: ['admin', 'user'],
          pic: '',
          language: 'en',
          is_admin: true,
          email_verified: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
      ];
      this.saveUsers(defaultUsers);
    }
  }

  // User management
  getUsers() {
    try {
      const users = localStorage.getItem(this.USERS_KEY);
      return users ? JSON.parse(users) : [];
    } catch (error) {
      console.error('Error getting users:', error);
      return [];
    }
  }

  saveUsers(users) {
    try {
      localStorage.setItem(this.USERS_KEY, JSON.stringify(users));
    } catch (error) {
      console.error('Error saving users:', error);
    }
  }

  findUserByEmail(email) {
    const users = this.getUsers();
    return users.find(user => user.email.toLowerCase() === email.toLowerCase());
  }

  findUserById(id) {
    const users = this.getUsers();
    return users.find(user => user.id === id);
  }

  createUser(userData) {
    const users = this.getUsers();
    const newUser = {
      id: `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      email_verified: false,
      roles: ['user'],
      is_admin: false,
      ...userData
    };
    users.push(newUser);
    this.saveUsers(users);
    return newUser;
  }

  updateUser(userId, updates) {
    const users = this.getUsers();
    const userIndex = users.findIndex(user => user.id === userId);
    if (userIndex !== -1) {
      users[userIndex] = {
        ...users[userIndex],
        ...updates,
        updated_at: new Date().toISOString()
      };
      this.saveUsers(users);
      return users[userIndex];
    }
    return null;
  }

  // Session management
  getSessions() {
    try {
      const sessions = localStorage.getItem(this.SESSIONS_KEY);
      return sessions ? JSON.parse(sessions) : [];
    } catch (error) {
      console.error('Error getting sessions:', error);
      return [];
    }
  }

  saveSessions(sessions) {
    try {
      localStorage.setItem(this.SESSIONS_KEY, JSON.stringify(sessions));
    } catch (error) {
      console.error('Error saving sessions:', error);
    }
  }

  createSession(userId) {
    const sessions = this.getSessions();
    const sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const accessToken = btoa(JSON.stringify({
      userId,
      sessionId,
      exp: Date.now() + 24 * 60 * 60 * 1000, // 24 hours
      iat: Date.now()
    }));
    const refreshToken = btoa(JSON.stringify({
      userId,
      sessionId,
      type: 'refresh',
      exp: Date.now() + 7 * 24 * 60 * 60 * 1000 // 7 days
    }));

    const session = {
      id: sessionId,
      userId,
      accessToken,
      refreshToken,
      created_at: new Date().toISOString(),
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      last_accessed: new Date().toISOString()
    };

    sessions.push(session);
    this.saveSessions(sessions);
    
    // Set as current session
    localStorage.setItem(this.CURRENT_SESSION_KEY, JSON.stringify(session));
    
    return {
      access_token: accessToken,
      refresh_token: refreshToken
    };
  }

  getCurrentSession() {
    try {
      const session = localStorage.getItem(this.CURRENT_SESSION_KEY);
      return session ? JSON.parse(session) : null;
    } catch (error) {
      console.error('Error getting current session:', error);
      return null;
    }
  }

  clearCurrentSession() {
    localStorage.removeItem(this.CURRENT_SESSION_KEY);
  }

  // Reset token management
  createResetToken(email) {
    const resetTokens = this.getResetTokens();
    const token = `reset-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const resetToken = {
      token,
      email,
      created_at: new Date().toISOString(),
      expires_at: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1 hour
      used: false
    };
    
    resetTokens.push(resetToken);
    this.saveResetTokens(resetTokens);
    return token;
  }

  getResetTokens() {
    try {
      const tokens = localStorage.getItem(this.RESET_TOKENS_KEY);
      return tokens ? JSON.parse(tokens) : [];
    } catch (error) {
      console.error('Error getting reset tokens:', error);
      return [];
    }
  }

  saveResetTokens(tokens) {
    try {
      localStorage.setItem(this.RESET_TOKENS_KEY, JSON.stringify(tokens));
    } catch (error) {
      console.error('Error saving reset tokens:', error);
    }
  }

  validateResetToken(token) {
    const resetTokens = this.getResetTokens();
    const resetToken = resetTokens.find(t => t.token === token && !t.used);
    if (!resetToken) return null;
    
    const now = Date.now();
    const expiresAt = new Date(resetToken.expires_at).getTime();
    if (now > expiresAt) return null;
    
    return resetToken;
  }

  useResetToken(token) {
    const resetTokens = this.getResetTokens();
    const tokenIndex = resetTokens.findIndex(t => t.token === token);
    if (tokenIndex !== -1) {
      resetTokens[tokenIndex].used = true;
      resetTokens[tokenIndex].used_at = new Date().toISOString();
      this.saveResetTokens(resetTokens);
    }
  }
}

/**
 * Local Authentication Service
 * Main authentication interface
 */
export class LocalAuth {
  constructor() {
    this.db = new LocalAuthDB();
  }

  // Authentication methods
  async login(email, password) {
    console.log('LocalAuth: Attempting login with email:', email);

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const user = this.db.findUserByEmail(email);
    if (!user || user.password !== password) {
      throw new Error('Invalid email or password');
    }

    console.log('LocalAuth: Login successful');
    return this.db.createSession(user.id);
  }

  async register(email, password, password_confirmation, firstName, lastName) {
    if (password !== password_confirmation) {
      throw new Error('Passwords do not match');
    }

    console.log('LocalAuth: Registering user:', email);

    // Check if user already exists
    const existingUser = this.db.findUserByEmail(email);
    if (existingUser) {
      throw new Error('User with this email already exists');
    }

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 800));

    const userData = {
      email,
      password, // In real app, this would be hashed
      username: email.split('@')[0],
      first_name: firstName || '',
      last_name: lastName || '',
      fullname: firstName && lastName ? `${firstName} ${lastName}`.trim() : '',
    };

    const user = this.db.createUser(userData);
    console.log('LocalAuth: User registered successfully:', user);

    // Auto-login after registration
    return this.db.createSession(user.id);
  }

  async resetPassword(email) {
    console.log('LocalAuth: Sending password reset email to:', email);

    const user = this.db.findUserByEmail(email);
    if (!user) {
      // Don't reveal if email exists or not for security
      console.log('LocalAuth: Password reset email sent (simulated)');
      await new Promise(resolve => setTimeout(resolve, 1000));
      return {
        success: true,
        message: 'Password reset email sent successfully'
      };
    }

    // Create reset token
    const token = this.db.createResetToken(email);
    console.log('LocalAuth: Reset token created:', token);

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
      success: true,
      message: 'Password reset email sent successfully',
      token // In real app, this would be sent via email
    };
  }

  async updatePassword(newPassword, resetToken = null) {
    console.log('LocalAuth: Updating password');

    if (resetToken) {
      // Password reset flow
      const tokenData = this.db.validateResetToken(resetToken);
      if (!tokenData) {
        throw new Error('Invalid or expired reset token');
      }

      const user = this.db.findUserByEmail(tokenData.email);
      if (!user) {
        throw new Error('User not found');
      }

      // Update password
      this.db.updateUser(user.id, { password: newPassword });
      this.db.useResetToken(resetToken);
    } else {
      // Regular password update (user is logged in)
      const session = this.db.getCurrentSession();
      if (!session) {
        throw new Error('User not authenticated');
      }

      try {
        const tokenData = JSON.parse(atob(session.accessToken));
        const user = this.db.findUserById(tokenData.userId);
        if (!user) {
          throw new Error('User not found');
        }

        this.db.updateUser(user.id, { password: newPassword });
      } catch (error) {
        throw new Error('Invalid session');
      }
    }

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 800));

    console.log('LocalAuth: Password updated successfully');
    return {
      success: true,
      message: 'Password updated successfully'
    };
  }

  async getCurrentUser() {
    const session = this.db.getCurrentSession();
    if (!session) return null;

    try {
      const tokenData = JSON.parse(atob(session.accessToken));
      
      // Check if token is expired
      if (Date.now() > tokenData.exp) {
        this.logout();
        return null;
      }

      const user = this.db.findUserById(tokenData.userId);
      if (!user) {
        this.logout();
        return null;
      }

      // Return user without password
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      console.error('Error getting current user:', error);
      this.logout();
      return null;
    }
  }

  async updateUserProfile(userData) {
    console.log('LocalAuth: Updating user profile:', userData);

    const session = this.db.getCurrentSession();
    if (!session) {
      throw new Error('User not authenticated');
    }

    try {
      const tokenData = JSON.parse(atob(session.accessToken));
      const updatedUser = this.db.updateUser(tokenData.userId, userData);
      
      if (!updatedUser) {
        throw new Error('User not found');
      }

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 500));

      console.log('LocalAuth: User profile updated successfully');
      
      // Return user without password
      const { password, ...userWithoutPassword } = updatedUser;
      return userWithoutPassword;
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw new Error('Failed to update profile');
    }
  }

  logout() {
    console.log('LocalAuth: Logging out user');
    this.db.clearCurrentSession();
  }

  // Utility methods
  isAuthenticated() {
    const session = this.db.getCurrentSession();
    if (!session) return false;

    try {
      const tokenData = JSON.parse(atob(session.accessToken));
      return Date.now() < tokenData.exp;
    } catch (error) {
      return false;
    }
  }

  getAuthToken() {
    const session = this.db.getCurrentSession();
    return session ? session.accessToken : null;
  }

  // Admin methods
  getAllUsers() {
    return this.db.getUsers().map(user => {
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    });
  }

  getAllSessions() {
    return this.db.getSessions();
  }

  clearAllData() {
    localStorage.removeItem(this.db.USERS_KEY);
    localStorage.removeItem(this.db.SESSIONS_KEY);
    localStorage.removeItem(this.db.CURRENT_SESSION_KEY);
    localStorage.removeItem(this.db.RESET_TOKENS_KEY);
    this.db.initializeDefaultUsers();
  }
}

// Create singleton instance
export const localAuth = new LocalAuth();

// Export for backward compatibility
export const LocalAuthAdapter = {
  login: (email, password) => localAuth.login(email, password),
  register: (email, password, password_confirmation, firstName, lastName) => 
    localAuth.register(email, password, password_confirmation, firstName, lastName),
  resetPassword: (email) => localAuth.resetPassword(email),
  updatePassword: (newPassword, resetToken) => localAuth.updatePassword(newPassword, resetToken),
  getCurrentUser: () => localAuth.getCurrentUser(),
  updateUserProfile: (userData) => localAuth.updateUserProfile(userData),
  logout: () => localAuth.logout(),
  requestPasswordReset: (email) => localAuth.resetPassword(email),
  resetPasswordWithToken: (password, password_confirmation, token) => {
    if (password !== password_confirmation) {
      throw new Error('Passwords do not match');
    }
    return localAuth.updatePassword(password, token);
  },
  resendVerificationEmail: async (email) => {
    console.log('LocalAuth: Verification email resent (simulated)');
    await new Promise(resolve => setTimeout(resolve, 500));
  },
  signInWithOAuth: async (provider, options) => {
    throw new Error('OAuth authentication is not available in local mode');
  }
};

export default localAuth;
