// Authentication helper functions

const AUTH_LOCAL_STORAGE_KEY = 'kt_auth_react_v';

/**
 * Save authentication data to localStorage
 */
export function saveAuth(auth) {
  if (auth) {
    localStorage.setItem(AUTH_LOCAL_STORAGE_KEY, JSON.stringify(auth));
  } else {
    localStorage.removeItem(AUTH_LOCAL_STORAGE_KEY);
  }
}

/**
 * Get authentication data from localStorage
 */
export function getAuth() {
  try {
    const auth = localStorage.getItem(AUTH_LOCAL_STORAGE_KEY);
    return auth ? JSON.parse(auth) : undefined;
  } catch (error) {
    console.error('Error getting auth from localStorage:', error);
    return undefined;
  }
}

/**
 * Remove authentication data from localStorage
 */
export function removeAuth() {
  localStorage.removeItem(AUTH_LOCAL_STORAGE_KEY);
}

/**
 * Check if user is authenticated
 */
export function isAuthenticated() {
  const auth = getAuth();
  return !!auth?.access_token;
}

/**
 * Get access token
 */
export function getAccessToken() {
  const auth = getAuth();
  return auth?.access_token;
}

/**
 * Get refresh token
 */
export function getRefreshToken() {
  const auth = getAuth();
  return auth?.refresh_token;
}
