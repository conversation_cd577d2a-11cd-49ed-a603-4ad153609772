// Local authentication adapter that provides authentication functionality
// without external dependencies. Uses localStorage and demo data.

/**
 * Local authentication adapter that provides all authentication functionality
 * using local storage and demo data instead of external services.
 */
export const LocalAuthAdapter = {
  /**
   * Login with email and password
   */
  async login(email, password) {
    console.log('LocalAdapter: Attempting login with email:', email);

    // Demo credentials
    const validCredentials = [
      { email: '<EMAIL>', password: 'demo123' },
      { email: '<EMAIL>', password: 'admin123' },
    ];

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Check credentials
    const isValid = validCredentials.some(
      cred => cred.email === email && cred.password === password
    );

    if (!isValid) {
      throw new Error('Invalid email or password');
    }

    console.log('LocalAdapter: Login successful');

    // Generate mock tokens
    const mockAccessToken = btoa(JSON.stringify({
      email,
      exp: Date.now() + 24 * 60 * 60 * 1000, // 24 hours
      iat: Date.now()
    }));

    const mockRefreshToken = btoa(JSON.stringify({
      email,
      type: 'refresh',
      exp: Date.now() + 7 * 24 * 60 * 60 * 1000 // 7 days
    }));

    return {
      access_token: mockAccessToken,
      refresh_token: mockRefreshToken,
    };
  },

  /**
   * Login with OAuth provider (Google, GitHub, etc.)
   */
  async signInWithOAuth(provider, options) {
    console.log('LocalAdapter: OAuth not supported in local mode');
    throw new Error('OAuth authentication is not available in local mode');
  },

  /**
   * Register a new user
   */
  async register(email, password, password_confirmation, firstName, lastName) {
    if (password !== password_confirmation) {
      throw new Error('Passwords do not match');
    }

    console.log('LocalAdapter: Registering user:', email);

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 800));

    // In local mode, we'll just simulate successful registration
    // You could store user data in localStorage here if needed
    const userData = {
      email,
      username: email.split('@')[0],
      first_name: firstName || '',
      last_name: lastName || '',
      fullname: firstName && lastName ? `${firstName} ${lastName}`.trim() : '',
      created_at: new Date().toISOString(),
    };

    console.log('LocalAdapter: User registered successfully:', userData);

    // Return empty tokens to simulate email confirmation requirement
    return {
      access_token: '',
      refresh_token: '',
    };
  },

  /**
   * Request password reset
   */
  async requestPasswordReset(email) {
    console.log('LocalAdapter: Requesting password reset for:', email);

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 600));

    console.log('LocalAdapter: Password reset email sent successfully (simulated)');

    return {
      success: true,
      message: 'Password reset email sent successfully'
    };
  },

  /**
   * Reset password (for reset password page)
   */
  async resetPassword(email) {
    console.log('LocalAdapter: Sending password reset email to:', email);

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // In local mode, we'll just simulate sending an email
    console.log('LocalAdapter: Password reset email sent successfully');

    return {
      success: true,
      message: 'Password reset email sent successfully'
    };
  },

  /**
   * Reset password with token
   */
  async resetPasswordWithToken(password, password_confirmation) {
    if (password !== password_confirmation) {
      throw new Error('Passwords do not match');
    }

    console.log('LocalAdapter: Password reset successful (simulated)');

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      success: true,
      message: 'Password reset successfully'
    };
  },

  /**
   * Update password (for change password page)
   */
  async updatePassword(newPassword) {
    console.log('LocalAdapter: Updating password');

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 800));

    // In local mode, we'll just simulate password update
    console.log('LocalAdapter: Password updated successfully');

    return {
      success: true,
      message: 'Password updated successfully'
    };
  },

  /**
   * Request another verification email
   */
  async resendVerificationEmail(email) {
    console.log('LocalAdapter: Resending verification email for:', email);

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('LocalAdapter: Verification email resent successfully (simulated)');
  },

  /**
   * Get current user from the session
   */
  async getCurrentUser() {
    return this.getUserProfile();
  },

  /**
   * Get user profile from user metadata
   */
  async getUserProfile() {
    // In local mode, return demo user data
    const demoUser = {
      id: 'demo-user-123',
      email: '<EMAIL>',
      email_verified: true,
      username: 'demo',
      first_name: 'Demo',
      last_name: 'User',
      fullname: 'Demo User',
      occupation: 'Developer',
      company_name: 'KT Company',
      companyName: 'KT Company', // For backward compatibility
      phone: '+1234567890',
      roles: ['user'],
      pic: '',
      language: 'en',
      is_admin: false,
    };

    console.log('LocalAdapter: Returning demo user profile');
    return demoUser;
  },

  /**
   * Update user profile (stored in metadata)
   */
  async updateUserProfile(userData) {
    console.log('LocalAdapter: Updating user profile:', userData);

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // In local mode, we could store this in localStorage if needed
    // For now, just simulate success
    console.log('LocalAdapter: User profile updated successfully (simulated)');

    return this.getCurrentUser();
  },

  /**
   * Logout the current user
   */
  async logout() {
    console.log('LocalAdapter: Logging out user');
    // No need to do anything special for local logout
    // The auth provider will handle clearing localStorage
  },
};
